package com.hx.frame.core.service;

import com.hx.frame.commons.base.service.IBaseService;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.dto.DtoConfig;

/**
 * 参数配置服务接口
 * 定义参数配置相关的业务逻辑方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface ConfigService extends IBaseService<DtoConfig> {

    /**
     * 检查参数键名是否唯一
     *
     * @param configKey 参数键名
     * @param id        参数配置ID（更新时使用，新增时为null）
     * @return 检查结果，true表示唯一，false表示已存在
     */
    Boolean checkConfigKeyUnique(String configKey, String id);

    /**
     * 根据参数键名获取参数值
     *
     * @param configKey 参数键名
     * @return 参数值
     */
    String getConfigValue(String configKey);

    /**
     * 根据参数键名获取参数配置
     *
     * @param configKey 参数键名
     * @return 参数配置对象
     */
    DtoConfig getConfigByKey(String configKey);
}
