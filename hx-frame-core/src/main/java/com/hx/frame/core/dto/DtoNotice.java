package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Notice;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 通知公告数据传输对象
 * 用于与前端交互的通知公告数据对象，继承自Notice实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_notice")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoNotice extends Notice {
}
