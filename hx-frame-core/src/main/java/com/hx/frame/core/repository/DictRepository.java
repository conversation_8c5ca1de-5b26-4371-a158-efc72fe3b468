package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoDict;

import java.util.Collection;
import java.util.List;

/**
 * 字典数据仓库接口
 * 提供字典数据实体的数据访问方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-05-11
 */
public interface DictRepository extends IBaseRepository<DtoDict> {

    /**
     * 根据字典类型ID查询字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    List<DtoDict> findByDictTypeIdOrderBySortAsc(String dictTypeId);

    /**
     * 根据字典类型ID和字典键值查询字典数据
     *
     * @param dictTypeId 字典类型ID
     * @param dictValue  字典键值
     * @return 字典数据
     */
    DtoDict findByDictTypeIdAndDictValue(String dictTypeId, String dictValue);

    /**
     * 根据字典类型ID集合查询字典数据列表
     *
     * @param dictTypeIds 字典类型ID集合
     * @return 字典数据列表
     */
    List<DtoDict> findByDictTypeIdIn(Collection<String> dictTypeIds);
}
