package com.hx.frame.core.entity;

import com.hx.frame.commons.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * 通知公告实体类
 * 存储系统通知公告信息，包括标题、内容、类型等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class Notice extends BaseEntity {

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（N-通知，Y-公告）
     */
    private String noticeType = "N";

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 状态（0-禁用，1-启用）
     */
    private Integer status = 1;

    /**
     * 排序值
     */
    private Integer sort = 0;

    /**
     * 备注
     */
    private String remark;
}
