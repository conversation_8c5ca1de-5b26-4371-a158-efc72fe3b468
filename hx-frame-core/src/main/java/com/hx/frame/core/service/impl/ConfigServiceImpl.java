package com.hx.frame.core.service.impl;

import com.hx.frame.commons.base.service.impl.BaseServiceImpl;
import com.hx.frame.commons.constant.CommonConstants;
import com.hx.frame.commons.exception.BusinessException;
import com.hx.frame.core.dto.DtoConfig;
import com.hx.frame.core.repository.ConfigRepository;
import com.hx.frame.core.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Optional;

/**
 * 参数配置服务实现类
 * 实现参数配置相关的业务逻辑
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Slf4j
@Service
public class ConfigServiceImpl extends BaseServiceImpl<DtoConfig, ConfigRepository> implements ConfigService {

    /**
     * 重写保存方法，添加参数键名唯一性检查
     *
     * @param config 参数配置实体
     * @return 保存后的参数配置实体
     */
    @Override
    @Transactional
    public DtoConfig save(DtoConfig config) {
        // 检查参数键名是否唯一
        if (checkConfigKeyUnique(config.getConfigKey(), null)) {
            throw new BusinessException("新增参数配置'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        return super.save(config);
    }

    /**
     * 重写更新方法，添加参数键名唯一性检查
     *
     * @param config 参数配置实体
     * @return 更新后的参数配置实体
     */
    @Override
    @Transactional
    public DtoConfig update(DtoConfig config) {
        // 检查参数键名是否唯一
        if (checkConfigKeyUnique(config.getConfigKey(), config.getId())) {
            throw new BusinessException("修改参数配置'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        return super.update(config);
    }

    /**
     * 重写逻辑删除方法，添加系统内置参数保护
     *
     * @param ids 要删除的ID集合
     */
    @Override
    @Transactional
    public void logicDelete(Collection<String> ids) {
        // 检查是否包含系统内置参数
        for (String id : ids) {
            DtoConfig config = findOne(id);
            if (config != null && CommonConstants.Config.CONFIG_TYPE_SYSTEM.equals(config.getConfigType())) {
                throw new BusinessException("参数配置'" + config.getConfigName() + "'是系统内置参数，不允许删除");
            }
        }
        super.logicDelete(ids);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getConfigValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }
        DtoConfig config = repository.findByConfigKey(configKey);
        return config == null ? null : config.getConfigValue();
    }

    /**
     * 检查参数键名是否唯一
     *
     * @param configKey 参数键名
     * @param id        参数配置ID（更新时使用，新增时为null）
     * @return 检查结果，true表示唯一，false表示已存在
     */
    private boolean checkConfigKeyUnique(String configKey, String id) {
        DtoConfig config = repository.findByConfigKey(configKey);
        if (config == null) {
            return false;
        }

        // 如果是更新操作，且找到的是当前角色，则不算重复
        return !StringUtils.hasText(id) || !config.getId().equals(id);
    }
}
