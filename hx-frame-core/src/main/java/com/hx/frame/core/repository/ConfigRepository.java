package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoConfig;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 参数配置数据访问接口
 * 提供参数配置相关的数据库操作方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Repository
public interface ConfigRepository extends IBaseRepository<DtoConfig> {

    /**
     * 根据参数键名查找参数配置
     *
     * @param configKey 参数键名
     * @return 参数配置对象
     */
    Optional<DtoConfig> findByConfigKey(String configKey);

    /**
     * 检查参数键名是否存在（排除指定ID）
     *
     * @param configKey 参数键名
     * @param id        排除的ID
     * @return 是否存在
     */
    boolean existsByConfigKeyAndIdNot(String configKey, String id);
}
