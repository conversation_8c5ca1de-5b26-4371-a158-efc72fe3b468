package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoNotice;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 通知公告查询条件类
 * 用于构建通知公告查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NoticeQueryCondition extends BaseQueryCondition<DtoNotice> {

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（N-通知，Y-公告）
     */
    private String noticeType;

    /**
     * 状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 构建查询条件
     * 将查询参数转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加公告标题模糊查询条件
        if (StringUtils.hasText(noticeTitle)) {
            like("noticeTitle", noticeTitle);
        }

        // 添加公告类型精确查询条件
        if (StringUtils.hasText(noticeType)) {
            eq("noticeType", noticeType);
        }

        // 添加状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
