package com.hx.frame.commons.constant;

/**
 * 系统通用常量类
 * 定义系统中使用的通用常量，避免硬编码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-04-27
 */
public class CommonConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private CommonConstants() {
        throw new IllegalStateException("常量类不允许实例化");
    }

    /**
     * 系统状态常量
     */
    public static class Status {
        /**
         * 启用状态
         */
        public static final Integer ENABLED = 1;

        /**
         * 禁用状态
         */
        public static final Integer DISABLED = 0;

        /**
         * 删除标记 - 已删除
         */
        public static final Boolean DELETED = true;

        /**
         * 删除标记 - 未删除
         */
        public static final Boolean NOT_DELETED = false;
    }

    /**
     * HTTP请求相关常量
     */
    public static class Http {
        /**
         * 授权请求头名称
         */
        public static final String AUTHORIZATION_HEADER = "Authorization";

        /**
         * Bearer认证前缀
         */
        public static final String BEARER_PREFIX = "Bearer ";
    }

    /**
     * 安全相关常量
     */
    public static class Security {
        /**
         * 管理员角色
         */
        public static final String ROLE_ADMIN = "ROLE_ADMIN";

        /**
         * 用户角色
         */
        public static final String ROLE_USER = "ROLE_USER";
    }

    /**
     * 分页相关常量
     */
    public static class Pagination {
        /**
         * 默认页码
         */
        public static final int DEFAULT_PAGE_NUMBER = 1;

        /**
         * 默认每页记录数
         */
        public static final int DEFAULT_PAGE_SIZE = 10;

        /**
         * 最大每页记录数
         */
        public static final int MAX_PAGE_SIZE = 100;
    }

    /**
     * 参数配置相关常量
     */
    public static class Config {
        /**
         * 系统内置参数类型 - 是
         */
        public static final Integer CONFIG_TYPE_SYSTEM = 1;

        /**
         * 系统内置参数类型 - 否
         */
        public static final Integer CONFIG_TYPE_USER = 0;
    }

    /**
     * 通知公告相关常量
     */
    public static class Notice {
        /**
         * 公告类型 - 通知
         */
        public static final String NOTICE_TYPE_NOTICE = "N";

        /**
         * 公告类型 - 公告
         */
        public static final String NOTICE_TYPE_ANNOUNCEMENT = "Y";
    }
}
