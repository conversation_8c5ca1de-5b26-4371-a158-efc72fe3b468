package com.hx.frame.core.repository;

import com.hx.frame.commons.base.repository.IBaseRepository;
import com.hx.frame.core.dto.DtoNotice;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 通知公告数据访问接口
 * 提供通知公告相关的数据库操作方法
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@Repository
public interface NoticeRepository extends IBaseRepository<DtoNotice> {

    /**
     * 根据公告类型查询公告列表
     *
     * @param noticeType 公告类型
     * @param sort       排序规则
     * @return 公告列表
     */
    List<DtoNotice> findByNoticeTypeOrderBySortAsc(String noticeType, Sort sort);

    /**
     * 根据状态查询公告列表
     *
     * @param status 状态
     * @param sort   排序规则
     * @return 公告列表
     */
    List<DtoNotice> findByStatusOrderBySortAsc(Integer status, Sort sort);

    /**
     * 根据公告类型和状态查询公告列表
     *
     * @param noticeType 公告类型
     * @param status     状态
     * @param sort       排序规则
     * @return 公告列表
     */
    List<DtoNotice> findByNoticeTypeAndStatusOrderBySortAsc(String noticeType, Integer status, Sort sort);

    /**
     * 查询所有公告并按排序值升序排列
     *
     * @return 公告列表
     */
    List<DtoNotice> findAllByOrderBySortAsc();
}
