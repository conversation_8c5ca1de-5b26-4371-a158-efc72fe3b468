package com.hx.frame.core.dto;

import com.hx.frame.core.entity.Config;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 参数配置数据传输对象
 * 用于与前端交互的参数配置数据对象，继承自Config实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "hx_frame_config")
@Where(clause = " deleted = 0 ")
@Data
@DynamicInsert
public class DtoConfig extends Config {
}
