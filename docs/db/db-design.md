# hx-frame 后台管理系统数据库设计文档

## 一、引言

本数据库设计文档旨在详细描述 hx-frame 后台管理系统的数据库设计，包括数据库表结构、字段定义、关系模型等内容，为系统的开发、测试和维护提供数据层面的指导。

## 二、数据库概述

hx-frame 后台管理系统采用 MySQL 数据库，具有性能稳定、易于维护、功能强大等特点，能够满足系统对数据存储和管理的需求。

## 三、数据库表结构设计

### （一）用户表（hx_frame_user）

 字段名           | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
---------------|--------------|------|------|-------------------|-------------------
 id            | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 username      | VARCHAR(50)  | 否    | 是    | 用户名               | -                 
 password      | VARCHAR(100) | 否    | 是    | 密码                | -                 
 real_name     | VARCHAR(50)  | 否    | 是    | 真实姓名              | -                 
 email         | VARCHAR(100) | 否    | 否    | 邮箱                | -                 
 phone         | VARCHAR(20)  | 否    | 否    | 手机号               | -                 
 status        | INT          | 否    | 是    | 用户状态（0-禁用，1-启用）   | 1                 
 department_id | VARCHAR(50)  | 否    | 否    | 部门ID              | -                 
 post_id       | VARCHAR(50)  | 否    | 否    | 岗位ID              | -                 
 avatar        | VARCHAR(100) | 否    | 否    | 头像地址              | -                 
 sort          | INT          | 否    | 否    | 排序值               | 0                 
 remark        | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted       | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version       | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by    | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time  | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by    | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time  | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （二）角色表（hx_frame_role）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 role_name    | VARCHAR(50)  | 否    | 是    | 角色名称              | -                 
 role_key     | VARCHAR(50)  | 否    | 是    | 角色权限字符串           | -                 
 status       | INT          | 否    | 是    | 角色状态（0-禁用，1-启用）   | 1                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （三）菜单表（hx_frame_menu）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述                 | 默认值               
--------------|--------------|------|------|----------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID                 | -                 
 menu_name    | VARCHAR(50)  | 否    | 是    | 菜单名称                 | -                 
 parent_id    | VARCHAR(50)  | 否    | 是    | 父菜单ID                | -                 
 path         | VARCHAR(200) | 否    | 否    | 路由地址                 | -                 
 component    | VARCHAR(200) | 否    | 否    | 组件路径                 | -                 
 is_frame     | INT          | 否    | 是    | 是否为外链（0-否，1-是）       | 0                 
 is_cache     | INT          | 否    | 是    | 是否缓存（0-否，1-是）        | 0                 
 menu_type    | CHAR(1)      | 否    | 是    | 菜单类型（M-目录 C-菜单 F-按钮） | -                 
 visible      | INT          | 否    | 是    | 菜单状态（0-隐藏，1-显示）      | 1                 
 status       | INT          | 否    | 是    | 菜单状态（0-禁用，1-启用）      | 1                 
 sort         | INT          | 否    | 否    | 排序值                  | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                   | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除）    | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号               | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人                  | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间                 | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人                  | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间                 | CURRENT_TIMESTAMP 

### （四）部门表（hx_frame_dept）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 dept_name    | VARCHAR(50)  | 否    | 是    | 部门名称              | -                 
 parent_id    | VARCHAR(50)  | 否    | 是    | 父部门ID             | -                 
 status       | INT          | 否    | 是    | 部门状态（0-禁用，1-启用）   | 1                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （五）字典类型表（hx_frame_dict_type）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 dict_name    | VARCHAR(50)  | 否    | 是    | 字典名称              | -                 
 dict_type    | VARCHAR(50)  | 否    | 是    | 字典类型              | -                 
 status       | INT          | 否    | 是    | 状态（0-禁用，1-启用）     | 1                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （六）字典数据表（hx_frame_dict）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 dict_type_id | VARCHAR(50)  | 否    | 是    | 字典类型ID            | -                 
 dict_label   | VARCHAR(50)  | 否    | 是    | 字典标签              | -                 
 dict_value   | VARCHAR(50)  | 否    | 是    | 字典键值              | -                 
 status       | INT          | 否    | 是    | 状态（0-禁用，1-启用）     | 1                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （七）参数配置表（hx_frame_config）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 config_name  | VARCHAR(50)  | 否    | 是    | 参数名称              | -                 
 config_key   | VARCHAR(50)  | 否    | 是    | 参数键名              | -                 
 config_value | VARCHAR(100) | 否    | 是    | 参数键值              | -                 
 config_type  | CHAR(1)      | 否    | 是    | 系统内置（Y-是，N-否）     | Y                 
 status       | INT          | 否    | 是    | 状态（0-禁用，1-启用）     | 1                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （八）通知公告表（hx_frame_notice）

 字段名            | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
----------------|--------------|------|------|-------------------|-------------------
 id             | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 notice_title   | VARCHAR(50)  | 否    | 是    | 公告标题              | -                 
 notice_type    | CHAR(1)      | 否    | 是    | 公告类型（N-通知，Y-公告）   | N                 
 notice_content | TEXT         | 否    | 是    | 公告内容              | -                 
 status         | INT          | 否    | 是    | 状态（0-禁用，1-启用）     | 1                 
 sort           | INT          | 否    | 否    | 排序值               | 0                 
 remark         | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted        | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version        | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by     | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time   | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by     | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time   | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （九）登录日志表（hx_frame_logininfo）

 字段名            | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
----------------|--------------|------|------|-------------------|-------------------
 id             | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 user_name      | VARCHAR(50)  | 否    | 是    | 用户账号              | -                 
 ipaddr         | VARCHAR(50)  | 否    | 是    | 登录IP地址            | -                 
 login_location | VARCHAR(255) | 否    | 是    | 登录地点              | -                 
 browser        | VARCHAR(50)  | 否    | 是    | 浏览器类型             | -                 
 os             | VARCHAR(50)  | 否    | 是    | 操作系统              | -                 
 status         | CHAR(1)      | 否    | 是    | 登录状态（N-失败，Y-成功）   | Y                 
 msg            | VARCHAR(255) | 否    | 是    | 提示信息              | -                 
 login_time     | DATETIME     | 否    | 是    | 访问时间              | -                 
 sort           | INT          | 否    | 否    | 排序值               | 0                 
 remark         | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted        | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version        | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by     | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time   | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by     | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time   | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 

### （十）文件表（hx_frame_file）

 字段名          | 字段类型         | 是否主键 | 是否必填 | 字段描述              | 默认值               
--------------|--------------|------|------|-------------------|-------------------
 id           | VARCHAR(50)  | 是    | 是    | 主键ID              | -                 
 file_name    | VARCHAR(100) | 否    | 是    | 文件名称              | -                 
 file_path    | VARCHAR(200) | 否    | 是    | 文件路径              | -                 
 file_type    | VARCHAR(50)  | 否    | 是    | 文件类型              | -                 
 sort         | INT          | 否    | 否    | 排序值               | 0                 
 remark       | VARCHAR(255) | 否    | 否    | 备注                | -                 
 deleted      | BIT(1)       | 否    | 是    | 删除标志（0-未删除，1-已删除） | 0                 
 version      | INT          | 否    | 是    | 乐观锁版本号            | 1                 
 created_by   | VARCHAR(50)  | 否    | 是    | 创建人               | -                 
 created_time | DATETIME     | 否    | 是    | 创建时间              | CURRENT_TIMESTAMP 
 updated_by   | VARCHAR(50)  | 否    | 否    | 更新人               | -                 
 updated_time | DATETIME     | 否    | 否    | 更新时间              | CURRENT_TIMESTAMP 