package com.hx.frame.core.controller;

import com.hx.frame.commons.base.controller.BaseController;
import com.hx.frame.commons.response.Result;
import com.hx.frame.core.condition.ConfigQueryCondition;
import com.hx.frame.core.dto.DtoConfig;
import com.hx.frame.core.service.ConfigService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * 参数配置控制器
 * 提供参数配置管理相关的RESTful API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/configs")
public class ConfigController extends BaseController<DtoConfig, ConfigService> {

    /**
     * 创建参数配置
     * 创建新的参数配置记录
     *
     * @param config 参数配置数据对象
     * @return 创建成功的参数配置对象
     */
    @PostMapping
    public Result<DtoConfig> create(@RequestBody DtoConfig config) {
        // 调用服务层保存参数配置数据
        return Result.success(service.save(config));
    }

    /**
     * 更新参数配置
     * 更新现有的参数配置记录
     *
     * @param config 参数配置数据对象（包含ID）
     * @return 更新后的参数配置对象
     */
    @PutMapping
    public Result<DtoConfig> update(@RequestBody DtoConfig config) {
        // 调用服务层更新参数配置数据
        return Result.success(service.update(config));
    }

    /**
     * 根据ID获取参数配置
     * 获取指定ID的参数配置详细信息
     *
     * @param id 参数配置ID
     * @return 参数配置对象
     */
    @GetMapping("/{id}")
    public Result<DtoConfig> getById(@PathVariable("id") String id) {
        // 调用服务层获取参数配置数据
        return Result.success(service.findOne(id));
    }

    /**
     * 批量删除参数配置
     * 根据ID列表批量删除参数配置
     *
     * @param ids 参数配置ID列表
     * @return 操作结果
     */
    @DeleteMapping
    public Result<Void> delete(@RequestBody Collection<String> ids) {
        // 调用服务层执行逻辑删除
        service.logicDelete(ids);
        return Result.success();
    }

    /**
     * 参数配置查询接口
     *
     * @param condition 查询条件
     * @return 参数配置分页结果
     */
    @PostMapping("/list")
    public Result<Page<DtoConfig>> query(@RequestBody ConfigQueryCondition condition) {
        // 调用服务层进行条件查询
        Page<DtoConfig> result = service.findByCondition(condition);
        return Result.success(result);
    }

    /**
     * 根据参数键名获取参数值
     *
     * @param configKey 参数键名
     * @return 参数值
     */
    @GetMapping("/value/{configKey}")
    public Result<String> getConfigValue(@PathVariable("configKey") String configKey) {
        String value = service.getConfigValue(configKey);
        return Result.success(value);
    }
}
