package com.hx.frame.core.condition;

import com.hx.frame.commons.query.BaseQueryCondition;
import com.hx.frame.core.dto.DtoConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 参数配置查询条件类
 * 用于构建参数配置查询的条件参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConfigQueryCondition extends BaseQueryCondition<DtoConfig> {

    /**
     * 参数名称
     */
    private String configName;

    /**
     * 参数键名
     */
    private String configKey;

    /**
     * 系统内置（Y-是，N-否）
     */
    private String configType;

    /**
     * 状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 构建查询条件
     * 将查询参数转换为查询条件
     */
    @Override
    public void buildConditions() {
        // 清空已有条件
        getConditions().clear();

        // 添加参数名称模糊查询条件
        if (StringUtils.hasText(configName)) {
            like("configName", configName);
        }

        // 添加参数键名模糊查询条件
        if (StringUtils.hasText(configKey)) {
            like("configKey", configKey);
        }

        // 添加系统内置类型精确查询条件
        if (StringUtils.hasText(configType)) {
            eq("configType", configType);
        }

        // 添加状态精确查询条件
        if (status != null) {
            eq("status", status);
        }

        // 排除已删除记录
        eq("deleted", false);
    }
}
